#!/usr/bin/env python3
import subprocess
import time
import logging
import os
import shutil
import datetime
import requests
import json

def run_command(cmd, timeout=10):
    """执行Shell命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            timeout=timeout,
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True
        )
        return result
    except subprocess.TimeoutExpired:
        return None

def connect_to_wifi_with_retry(interface, ssid, max_retries=5, retry_delay=15):
    """重试连接WiFi，最多尝试max_retries次"""
    for attempt in range(1, max_retries + 1):
        logging.info(f"尝试连接到 {ssid} (第 {attempt}/{max_retries} 次)...")
        result = run_command(f"nmcli dev wifi connect '{ssid}' ifname {interface}")
        
        if result and result.returncode == 0:
            logging.info(f"成功连接到 {ssid}")
            return True
        else:
            error = result.stderr if result else "超时"
            logging.warning(f"连接失败: {error.strip()}")
            
            if attempt < max_retries:
                logging.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
    
    logging.error(f"无法连接到 {ssid} (尝试 {max_retries} 次后失败)")
    return False

def parse_version_file(file_path):
    """解析版本文件并返回字典"""
    version_data = {}
    if not os.path.exists(file_path):
        logging.warning(f"版本文件不存在: {file_path}")
        return version_data
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if '=' in line:
                    key, value = line.split('=', 1)
                    version_data[key.strip()] = value.strip()
    except Exception as e:
        logging.error(f"解析版本文件失败: {str(e)}")
    
    return version_data

def analyze_log_files(ssid, base_dir, start_time, end_time):
    """分析日志文件，统计跨区域和回充功能成功次数（按时间过滤）"""
    target_dir = os.path.join(base_dir, ssid)
    cross_region_count = 0
    recharge_count = 0
    
    # 定义要分析的日志文件模式
    log_files = [
        "mcu_communication_node.log",
        "mcu_communication_node.1.log", 
        "mcu_communication_node.2.log",
        "mcu_communication_node.3.log",
        "mcu_communication_node.4.log",
        "mcu_communication_node.5.log",
        "mcu_communication_node.6.log",
        "mcu_communication_node.7.log",
        "mcu_communication_node.8.log",
        "mcu_communication_node.9.log",
        "mcu_communication_node.10.log"
    ]
    
    # 定义关键词
    cross_region_keyword = "cross_region_result.cross_region_result_type SUCCESS"
    recharge_keyword = "recharge_result.result true"
    
    logging.info(f"开始分析 {ssid} 的日志文件（时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}）...")
    
    for log_file in log_files:
        log_path = os.path.join(target_dir, log_file)
        if not os.path.exists(log_path):
            logging.debug(f"日志文件不存在: {log_path}")
            continue
            
        try:
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 解析日志时间
                    log_time = parse_log_time(line)
                    if log_time is None:
                        # 如果无法解析时间，跳过这行
                        continue
                    
                    # 检查时间是否在指定范围内
                    if not (start_time <= log_time <= end_time):
                        continue
                    
                    # 检查跨区域功能成功
                    if cross_region_keyword in line:
                        cross_region_count += 1
                        logging.debug(f"在 {log_file}:{line_num} 找到跨区域成功记录 (时间: {log_time.strftime('%Y-%m-%d %H:%M:%S')})")
                    
                    # 检查回充功能成功
                    if recharge_keyword in line:
                        recharge_count += 1
                        logging.debug(f"在 {log_file}:{line_num} 找到回充成功记录 (时间: {log_time.strftime('%Y-%m-%d %H:%M:%S')})")
                        
        except Exception as e:
            logging.error(f"分析日志文件 {log_file} 时出错: {str(e)}")
            continue
    
    logging.info(f"{ssid} 日志分析完成 - 跨区域成功: {cross_region_count}次, 回充成功: {recharge_count}次")
    return cross_region_count, recharge_count

def parse_log_time(log_line):
    """解析日志行中的时间戳"""
    import re
    
    # 匹配格式: [2025-09-19 11:14:32.180]
    time_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]'
    match = re.match(time_pattern, log_line)
    
    if match:
        try:
            time_str = match.group(1)
            # 解析时间字符串，忽略毫秒部分
            time_str_without_ms = time_str.split('.')[0]  # 去掉毫秒部分
            log_time = datetime.datetime.strptime(time_str_without_ms, '%Y-%m-%d %H:%M:%S')
            return log_time
        except ValueError as e:
            logging.debug(f"时间解析失败: {time_str}, 错误: {str(e)}")
            return None
    else:
        return None

def generate_md_report(ssid, robot_id, base_dir, start_time, end_time, connected, download_success, cross_region_count=0, recharge_count=0):
    """生成机器人的Markdown格式测试报告"""
    # 生成标题
    md_report = f"\n## {robot_id}号机 ({ssid}) 测试信息\n"
    
    # 构建文件路径
    software_file = os.path.join(base_dir, ssid, "soc_software_version.txt")
    algorithm_file = os.path.join(base_dir, ssid, "soc_algorithm_version.txt")
    
    # 解析版本文件
    software_data = parse_version_file(software_file) if connected and download_success else {}
    algorithm_data = parse_version_file(algorithm_file) if connected and download_success else {}
    
    # 格式化日期时间
    start_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    end_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 创建信息表格
    md_report += "| 项目 | 值 |\n"
    md_report += "|------|------|\n"
    md_report += f"| 机器人编号 | {robot_id} |\n"
    md_report += f"| WiFi名称 | {ssid} |\n"
    md_report += f"| 测试开始时间 | {start_str} |\n"
    md_report += f"| 测试结束时间 | {end_str} |\n"
    
    # 添加软件版本信息（如果成功连接并下载）
    if connected and download_success:
        md_report += f"| 软件版本 | {software_data.get('soc_software_version', 'N/A')} |\n"
        md_report += f"| 软件构建时间 | {software_data.get('build_time', 'N/A')} |\n"
        md_report += f"| 软件Git哈希 | {software_data.get('git_hash', 'N/A')} |\n"
        md_report += f"| 算法版本 | {algorithm_data.get('soc_algorithm_version', 'N/A')} |\n"
        md_report += f"| 算法构建时间 | {algorithm_data.get('build_time', 'N/A')} |\n"
        md_report += f"| 算法Commit ID | {algorithm_data.get('commit_id', 'N/A')} |\n"
        md_report += f"| 跨区域功能成功次数 | {cross_region_count} |\n"
        md_report += f"| 回充功能成功次数 | {recharge_count} |\n"
    
    # 添加错误信息（如果有）
    if not connected:
        md_report += f"| 错误信息 | 连接WiFi失败 |\n"
    elif not download_success:
        md_report += f"| 错误信息 | 文件下载失败 |\n"
    
    md_report += "\n"
    return md_report

def generate_text_report(ssid, robot_id, base_dir, start_time, end_time, connected, download_success, cross_region_count=0, recharge_count=0):
    """生成机器人的文本格式测试报告（手机端友好）"""
    # 生成标题
    text_report = f"\n{robot_id}号机 ({ssid}) 测试信息\n"
    text_report += "=" * 40 + "\n"
    
    # 构建文件路径
    software_file = os.path.join(base_dir, ssid, "soc_software_version.txt")
    algorithm_file = os.path.join(base_dir, ssid, "soc_algorithm_version.txt")
    
    # 解析版本文件
    software_data = parse_version_file(software_file) if connected and download_success else {}
    algorithm_data = parse_version_file(algorithm_file) if connected and download_success else {}
    
    # 格式化日期时间
    start_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    end_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 创建信息列表
    text_report += f"机器人编号: {robot_id}\n"
    text_report += f"WiFi名称: {ssid}\n"
    text_report += f"测试开始时间: {start_str}\n"
    text_report += f"测试结束时间: {end_str}\n"
    
    # 添加软件版本信息（如果成功连接并下载）
    if connected and download_success:
        text_report += f"软件版本: {software_data.get('soc_software_version', 'N/A')}\n"
        text_report += f"软件构建时间: {software_data.get('build_time', 'N/A')}\n"
        text_report += f"软件Git哈希: {software_data.get('git_hash', 'N/A')}\n"
        text_report += f"算法版本: {algorithm_data.get('soc_algorithm_version', 'N/A')}\n"
        text_report += f"算法构建时间: {algorithm_data.get('build_time', 'N/A')}\n"
        text_report += f"算法Commit ID: {algorithm_data.get('commit_id', 'N/A')}\n"
        
        # 添加日志分析结果
        text_report += f"跨区域功能成功次数: {cross_region_count}\n"
        text_report += f"回充功能成功次数: {recharge_count}\n"
    
    # 添加错误信息（如果有）
    if not connected:
        text_report += f"错误信息: 连接WiFi失败\n"
    elif not download_success:
        text_report += f"错误信息: 文件下载失败\n"
    
    text_report += "\n"
    return text_report

def calculate_test_start_time():
    """计算测试开始时间（前一天晚上10点）"""
    # 获取当前时间
    now = datetime.datetime.now()
    
    # 计算前一天日期
    previous_day = now - datetime.timedelta(days=1)
    
    # 设置时间为晚上10点 (22:00)
    start_time = datetime.datetime(
        year=previous_day.year,
        month=previous_day.month,
        day=previous_day.day,
        hour=22,
        minute=0,
        second=0
    )
    
    return start_time

def download_robot_files(ssid, robot_id, base_dir, start_time, end_time):
    """从机器人下载文件到本地目录（带重试机制）"""
    # 创建目标目录
    target_dir = os.path.join(base_dir, ssid)
    if os.path.exists(target_dir):
        shutil.rmtree(target_dir)
    os.makedirs(target_dir, exist_ok=True)
    logging.info(f"创建下载目录: {target_dir}")
    
    # 待下载的文件列表（可扩展）
    file_list = [
        "/userdata/log/mcu_communication_node.log",
        "/userdata/log/mcu_communication_node.1.log",
        "/userdata/log/mcu_communication_node.2.log",
        "/userdata/log/mcu_communication_node.3.log",
        "/userdata/log/mcu_communication_node.4.log",
        "/userdata/log/mcu_communication_node.5.log",
        "/userdata/log/mcu_communication_node.6.log",
        "/userdata/log/mcu_communication_node.7.log",
        "/userdata/log/mcu_communication_node.8.log",
        "/userdata/log/mcu_communication_node.9.log",
        "/userdata/log/mcu_communication_node.10.log",
        "/app/active_slot/mower_algorithm/soc_algorithm_version.txt",
        "/app/active_slot/mower_software/soc_software_version.txt"
    ]
    
    # 机器人登录信息
    robot_user = "root"
    robot_ip = "********"
    robot_pass = "12345678"
    
    # 文件下载状态
    all_files_downloaded = True
    
    # 下载每个文件（带重试机制）
    for remote_file in file_list:
        filename = os.path.basename(remote_file)
        local_path = os.path.join(target_dir, filename)
        
        # 每个文件最多重试3次
        max_retries = 3
        retry_delay = 5  # 重试间隔(秒)
        file_downloaded = False
        file_not_exists = False  # 标记文件是否不存在
        
        for attempt in range(1, max_retries + 1):
            logging.info(f"下载文件: {remote_file} → {local_path} (尝试 {attempt}/{max_retries})")
            
            # 执行SCP命令（超时60秒）
            cmd = f'sshpass -p "{robot_pass}" scp -o StrictHostKeyChecking=no {robot_user}@{robot_ip}:{remote_file} "{local_path}"'
            result = run_command(cmd, timeout=60)
            
            if result and result.returncode == 0:
                logging.info(f"{filename} 下载成功")
                file_downloaded = True
                break  # 成功则跳出重试循环
            else:
                error = result.stderr if result else "超时"
                
                # 检查是否是文件不存在的错误
                if result and result.stderr and ("No such file or directory" in result.stderr or "not found" in result.stderr.lower()):
                    logging.info(f"{filename} 文件不存在，跳过下载")
                    file_not_exists = True
                    break  # 文件不存在，跳出重试循环
                else:
                    logging.warning(f"{filename} 下载失败: {error.strip()}")
                
                # 删除可能存在的部分下载的文件
                if os.path.exists(local_path):
                    try:
                        os.remove(local_path)
                        logging.debug(f"已删除部分下载的文件: {local_path}")
                    except Exception as e:
                        logging.error(f"无法删除文件 {local_path}: {str(e)}")
                
                # 如果还有重试机会，等待后重试
                if attempt < max_retries:
                    logging.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
        
        # 检查文件下载结果
        if file_not_exists:
            # 文件不存在，认为下载失败，立即中止该机器人的文件下载
            logging.error(f"{filename} 文件不存在，中止 {ssid} 的文件下载操作")
            all_files_downloaded = False
            break
        elif not file_downloaded:
            # 文件存在但下载失败，立即中止该机器人的文件下载
            logging.error(f"{filename} 下载失败，已达到最大重试次数，中止 {ssid} 的文件下载操作")
            all_files_downloaded = False
            break
    
    if all_files_downloaded:
        logging.info(f"{ssid} 的所有文件下载完成")
        return True
    else:
        logging.error(f"{ssid} 的文件下载操作失败")
        return False

def send_to_dingding(report, use_text_format=True):
    """发送报告到钉钉机器人"""
    # 钉钉机器人Webhook URL
    webhook_url = "https://oapi.dingtalk.com/robot/send?access_token=da994dcec774e83e435bcd8c8aae78f81e754670742eddf193597650e8314233"
    
    # 添加关键词前缀（钉钉要求）
    full_report = f"StressTest\n{report}"
    
    # 根据格式选择消息类型
    if use_text_format:
        # 使用text格式（手机端友好）
        data = {
            "msgtype": "text",
            "text": {
                "content": full_report
            }
        }
    else:
        # 使用markdown格式（电脑端友好）
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": "机器人测试报告",
                "text": full_report
            }
        }
    
    # 设置请求头
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        response = requests.post(
            webhook_url,
            data=json.dumps(data),
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("errcode") == 0:
                logging.info("报告已发送到钉钉")
                return True
            else:
                logging.error(f"钉钉返回错误: {result.get('errmsg', '未知错误')}")
                return False
        else:
            logging.error(f"发送钉钉消息失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"发送钉钉消息时出错: {str(e)}")
        return False

def wait_until_trigger_time(trigger_time_str):
    """等待直到指定的触发时间"""
    # 解析触发时间字符串
    try:
        trigger_hour, trigger_minute = map(int, trigger_time_str.split(':'))
    except ValueError:
        logging.error("触发时间格式错误，请使用 HH:MM 格式")
        return False
    
    logging.info(f"等待触发时间: {trigger_time_str}")
    now = datetime.datetime.now()
    # 构建今天的触发时间
    trigger_today = datetime.datetime(
        year=now.year,
        month=now.month,
        day=now.day,
        hour=trigger_hour,
        minute=trigger_minute,
        second=0,
        microsecond=0
    )
    # 如果当前时间已经超过今天的触发时间，则设置明天的触发时间
    if now >= trigger_today:
        trigger_today = trigger_today + datetime.timedelta(days=1)
    
    while True:
        now = datetime.datetime.now()
        # 计算等待时间（秒）
        wait_seconds = (trigger_today - now).total_seconds()
        
        # 格式化为[时:分:秒]以便显示
        wait_time_str = time.strftime("%H:%M:%S", time.gmtime(wait_seconds))
        
        if wait_seconds <= 0:
            logging.info("触发时间已到，开始执行测试")
            return True
        
        # 每30秒输出一次等待信息
        logging.info(f"程序运行中，等待触发时间 {trigger_time_str} (剩余: {wait_time_str})")
        time.sleep(30)

def run_daily_test(WIFI_INTERFACE, ROBOT_WIFIS, ROBOT_IDS, MAX_CONNECTION_RETRIES, RETRY_DELAY, POST_DOWNLOAD_DELAY, USE_TEXT_FORMAT):
    """执行一次完整的日常测试"""
    # 计算测试时间
    start_time = calculate_test_start_time()  # 前一天晚上10点
    end_time = datetime.datetime.now()  # 当前时间作为测试结束时间
    logging.info(f"测试开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"测试结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确保无线接口已启用
    run_command(f"nmcli radio wifi on")
    run_command(f"nmcli dev set {WIFI_INTERFACE} managed yes")
    
    # 创建基础目录
    BASE_DIR = os.path.expanduser("~/pnc_robot_test")
    if os.path.exists(BASE_DIR):
        shutil.rmtree(BASE_DIR)
    os.makedirs(BASE_DIR)
    logging.info(f"创建基础目录: {BASE_DIR}")
    
    # 存储所有机器人报告的列表
    all_reports = []
    
    # 依次连接每个机器人WiFi
    for i, (ssid, robot_id) in enumerate(zip(ROBOT_WIFIS, ROBOT_IDS)):
        logging.info(f"\n=== 开始处理 {robot_id}号机 ({ssid}) [{i+1}/{len(ROBOT_WIFIS)}] ===")
        
        # 断开当前连接
        run_command(f"nmcli dev disconnect {WIFI_INTERFACE}")
        logging.info("已断开当前连接")
        
        # 尝试连接（带重试机制）
        connected = connect_to_wifi_with_retry(WIFI_INTERFACE, ssid, MAX_CONNECTION_RETRIES, RETRY_DELAY)
        download_success = False
        
        if connected:
            # 获取IP信息
            ip_result = run_command(f"ip -4 addr show {WIFI_INTERFACE}")
            if ip_result and ip_result.stdout:
                logging.info(f"网络信息:\n{ip_result.stdout.strip()}")
            
            # 下载机器人文件（带重试机制）
            download_success = download_robot_files(ssid, robot_id, BASE_DIR, start_time, end_time)
            
            # 文件下载完成后保持连接一段时间
            if download_success:
                logging.info(f"文件下载完成，保持连接 {POST_DOWNLOAD_DELAY} 秒...")
                time.sleep(POST_DOWNLOAD_DELAY)
            else:
                # 下载失败时立即断开连接
                run_command(f"nmcli dev disconnect {WIFI_INTERFACE}")
                logging.info("文件下载失败，已断开连接")
        else:
            logging.error(f"无法连接到 {ssid}，跳过下载")
        
        # 分析日志文件（如果下载成功）
        cross_region_count = 0
        recharge_count = 0
        if connected and download_success:
            cross_region_count, recharge_count = analyze_log_files(ssid, BASE_DIR, start_time, end_time)
        
        # 生成当前机器人的报告
        if USE_TEXT_FORMAT:
            report = generate_text_report(ssid, robot_id, BASE_DIR, start_time, end_time, connected, download_success, cross_region_count, recharge_count)
        else:
            report = generate_md_report(ssid, robot_id, BASE_DIR, start_time, end_time, connected, download_success, cross_region_count, recharge_count)
        all_reports.append(report)
        
        logging.info(f"=== 完成 {robot_id}号机 ({ssid}) 处理 ===\n")
    
    # 最终断开连接
    run_command(f"nmcli dev disconnect {WIFI_INTERFACE}")
    logging.info("所有机器人WiFi已切换完成")
    
    # 合并所有报告
    if USE_TEXT_FORMAT:
        # 使用文本格式（手机端友好）
        full_report = "机器人测试信息汇总\n"
        full_report += "=" * 50 + "\n"
        full_report += f"测试开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        full_report += f"测试结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        full_report += f"测试机器人数量: {len(ROBOT_WIFIS)}\n\n"
    else:
        # 使用Markdown格式（电脑端友好）
        full_report = "# 机器人测试信息汇总\n"
        full_report += f"**测试开始时间**: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        full_report += f"**测试结束时间**: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        full_report += f"**测试机器人数量**: {len(ROBOT_WIFIS)}\n\n"
    
    # 添加每个机器人的报告
    for report in all_reports:
        full_report += report
    
    # 在控制台输出完整报告
    print("\n" + "="*80)
    print(full_report)
    print("="*80)
    
    # 发送报告到钉钉
    logging.info("正在发送报告到钉钉机器人...")
    if send_to_dingding(full_report, USE_TEXT_FORMAT):
        logging.info("报告已发送到钉钉")
    else:
        logging.error("发送报告到钉钉失败")
    
    logging.info("本次测试完成")

def main():
    # ============== 配置参数 ==============
    WIFI_INTERFACE = "wlx08beac2acb9a"        # 用于连接机器人的无线网卡接口
    ROBOT_WIFIS = ["OB_MOWER_0b1b14", "OB_MOWER_042c19", "OB_MOWER_072b1b"]  # 机器人WiFi名称
    ROBOT_IDS = [5, 6, 14]  # 机器人编号数组，与ROBOT_WIFIS一一对应
    MAX_CONNECTION_RETRIES = 5                # 最大连接重试次数
    RETRY_DELAY = 15                          # 重试间隔(秒)
    POST_DOWNLOAD_DELAY = 20                  # 下载完成后的保持时间(秒)
    TRIGGER_TIME = "08:00"                    # 触发时间（格式：HH:MM）
    USE_TEXT_FORMAT = True                    # 是否使用文本格式（True=手机端友好，False=Markdown表格）
    # ======================================
    
    # 设置日志（增加详细程度）
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    logging.info("机器人WiFi切换守护程序启动")
    logging.info(f"无线网卡: {WIFI_INTERFACE}")
    logging.info(f"目标WiFi: {', '.join(ROBOT_WIFIS)}")
    logging.info(f"对应机器人编号: {ROBOT_IDS}")
    logging.info(f"最大连接重试次数: {MAX_CONNECTION_RETRIES}, 重试间隔: {RETRY_DELAY}秒")
    logging.info(f"文件下载重试次数: 3次, 重试间隔: 5秒")
    logging.info(f"触发时间: {TRIGGER_TIME}")
    logging.info("程序将持续运行，每天在指定时间执行测试")
    
    # 持续运行循环
    while True:
        try:
            # 等待直到触发时间
            wait_until_trigger_time(TRIGGER_TIME)
            
            # 执行日常测试
            run_daily_test(WIFI_INTERFACE, ROBOT_WIFIS, ROBOT_IDS, MAX_CONNECTION_RETRIES, RETRY_DELAY, POST_DOWNLOAD_DELAY, USE_TEXT_FORMAT)
            
            # 测试完成后，等待一段时间再开始下一轮等待
            logging.info("测试完成，等待下一轮测试...")
            time.sleep(60)  # 等待1分钟，避免立即重新开始
            
        except KeyboardInterrupt:
            logging.info("收到中断信号，程序即将退出")
            break
        except Exception as e:
            logging.error(f"程序运行出错: {str(e)}")
            logging.info("程序将在60秒后重试...")
            time.sleep(60)  # 出错后等待60秒再重试
    
    logging.info("机器人WiFi切换守护程序已退出")

if __name__ == "__main__":
    main()
