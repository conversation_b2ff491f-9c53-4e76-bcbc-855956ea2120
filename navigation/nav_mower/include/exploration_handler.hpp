#ifndef EXPLORATION_HANDLER_HPP
#define EXPLORATION_HANDLER_HPP

#include "data_type.hpp"
#include "mower_msgs/srv/explore_map_result.hpp"
#include "obstacle_classification.hpp"
#include "perception_status_filter.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"

#include <chrono>
#include <cmath>
#include <functional>
#include <limits>
#include <memory>
#include <mutex>

namespace fescue_iox
{

// Forward declarations
struct ExceptionInfo;
struct RegionExploreResult;

class ExplorationHandler
{
public:
    struct ExplorationConfig
    {
        // Exploration parameters
        float mower_linear{0.15};
        float mower_angular{0.5};
        int edge_mode_direction{-1};
        float mark_distance_threshold{0.5};
        float camera_2_center_dis{0.37};

        // Cooldown parameters
        int edge_perception_drive_cooldown_time_threshold{10};
        int qr_detection_cooldown_time_threshold{30};
        int mark_detection_cooldown_time_threshold{30};

        // Region exploration
        float recharge_distance_threshold{1.2};

        // Perception filter parameters
        int perception_filter_window_size{5};
        float perception_filter_confidence_threshold{0.6f};
        float perception_filter_debounce_time{1.0f};

        // Bottom obstacle avoidance
        float bottom_obstacle_avoidance_cooldown_time{5.0f};

        // Perception movement
        float perception_movement_debounce_time{2.0f};
    };

    struct ExplorationState
    {
        // Exploration mode state
        bool is_region_explore_mode_start{false};
        bool enter_multi_region_exploration{false};

        // Cooldown mechanism
        bool is_cooldown_active{false};
        bool is_first_enter_last_cooldown_time{true};
        std::chrono::steady_clock::time_point last_cooldown_time;
        std::chrono::seconds edge_perception_drive_duration{0};

        // Single region exploration
        int qr_code_detection_count{1};
        bool is_first_enter_explore_last_qr_detection_time{true};
        std::chrono::steady_clock::time_point last_qr_explore_detection_time;
        std::chrono::seconds qr_detection_duration{0};
        bool is_single_area_recharge{false};

        // Multi region exploration
        BeaconStatus beacon_status{-1, 0};
        int current_mark_id{-1};
        bool is_first_enter_last_mark_detection_time{true};
        std::chrono::steady_clock::time_point last_mark_detection_time;
        std::chrono::seconds mark_detection_duration{0};
        bool first_detection_beacon{true};
        int next_paired_beacon_id{-1};
        int region_count{1};

        // Cross recharge params
        bool first_recharge_cross_region{true};
        bool cross_recharge_detection_beacon{true};
        std::chrono::seconds recharge_cross_region_duration{0};
        std::chrono::steady_clock::time_point last_recharge_cross_region_time;
        int cross_recharge_mark_id{-1};
        BeaconStatus cross_recharge_beacon_status{-1, 0};
        bool recharge_edge_follow{false};
        float recharge_distance{1.3};
        float unstable_qrcode_yaw{1.2};

        // Region exploration results
        mower_msgs::srv::MapResult master_region_explore_result;
        mower_msgs::srv::MapResult slave_region_explore_result;
        bool is_master_region{true};
        bool is_first_region_explore_mode_end{true};

        // Perception movement state
        // PerceptionMovementState last_perception_movement_state{PerceptionMovementState::IDLE};
        std::chrono::steady_clock::time_point last_perception_movement_time;

        // Bottom obstacle avoidance
        std::chrono::steady_clock::time_point last_bottom_obstacle_avoidance_time;
    };

    // Callback types
    using VelocityPublishCallback = std::function<void(float, float, uint64_t)>;
    using FeatureUpdateCallback = std::function<void(ThreadControl)>;
    using EdgeFollowCallback = std::function<void()>;
    using CrossRegionCallback = std::function<void()>;
    using MarkIdCallback = std::function<bool(int)>;
    using CrossRegionStateCallback = std::function<void(CrossRegionRunningState)>;
    using AreaCalcCallback = std::function<bool(uint64_t)>;
    using AreaCalcStopCallback = std::function<bool(uint64_t, float &, float &)>;
    using RegionExploreResultCallback = std::function<void(RegionExploreResult &)>;
    using LinearMotionCallback = std::function<void(float, float, float, int, float, bool)>;

public:
    ExplorationHandler(const ExplorationConfig &config);
    ~ExplorationHandler() = default;

    // Main exploration function
    void PerformExploration(MarkLocationResult &mark_loc_result,
                            const QRCodeLocationResult &qrcode_loc_result,
                            const PerceptionFusionResult &fusion_result,
                            CrossRegionRunningState &cross_region_state,
                            BehaviorRunningState &behavior_state,
                            bool is_behavior_loop,
                            const std::vector<BehaviorExceptionType> &triggered_exception_types,
                            ThreadControl &thread_control);

    // Exploration mode processing
    void ProcessExplorationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result,
                                       bool is_enable_unstake_mode,
                                       bool is_unstake_success,
                                       bool is_power_connected,
                                       bool mcu_triggers_region_exploration);

    // Exception handling
    void HandleExplorationMcuException(const ExceptionInfo &exception_info,
                                       ThreadControl &thread_control);

    // State management
    void ResetExplorationState();
    const ExplorationState &GetState() const { return state_; }
    ExplorationState &GetState() { return state_; }

    // Callback setters
    void SetVelocityPublishCallback(VelocityPublishCallback callback) { velocity_publish_callback_ = callback; }
    void SetFeatureUpdateCallback(FeatureUpdateCallback callback) { feature_update_callback_ = callback; }
    void SetEdgeFollowDisableCallback(EdgeFollowCallback callback) { edge_follow_disable_callback_ = callback; }
    void SetEdgeFollowEnableCallback(EdgeFollowCallback callback) { edge_follow_enable_callback_ = callback; }
    void SetCrossRegionDisableCallback(CrossRegionCallback callback) { cross_region_disable_callback_ = callback; }
    void SetCrossRegionEnableCallback(CrossRegionCallback callback) { cross_region_enable_callback_ = callback; }
    void SetMarkIdCallback(MarkIdCallback callback) { mark_id_callback_ = callback; }
    void SetCrossRegionStateCallback(CrossRegionStateCallback callback) { cross_region_state_callback_ = callback; }
    void SetAreaCalcStartCallback(AreaCalcCallback callback) { area_calc_start_callback_ = callback; }
    void SetAreaCalcStopCallback(AreaCalcStopCallback callback) { area_calc_stop_callback_ = callback; }
    void SetRegionExploreResultCallback(RegionExploreResultCallback callback) { region_explore_result_callback_ = callback; }
    void SetLinearMotionCallback(LinearMotionCallback callback) { linear_motion_callback_ = callback; }

private:
    // Beacon detection and processing
    void ProcessBeaconDetection(MarkLocationResult &mark_loc_result,
                                const PerceptionFusionResult &fusion_result,
                                bool &enter_multi_region_exploration,
                                bool &is_beacon_valid,
                                ThreadControl &thread_control);

    void ProcessSingleAreaExplorationMode(const QRCodeLocationResult &qrcode_loc_result,
                                          const bool &enter_multi_region_exploration,
                                          ThreadControl &thread_control);

    void ProcessMultiAreaExplorationMode(const MarkLocationResult &mark_loc_result,
                                         const PerceptionFusionResult &fusion_result,
                                         const bool &enter_multi_region_exploration,
                                         bool &is_beacon_valid,
                                         ThreadControl &thread_control);

    void HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result,
                                             const PerceptionFusionResult &fusion_result,
                                             bool &is_cooldown_active,
                                             ThreadControl &thread_control);

    void HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result,
                                     const PerceptionFusionResult &fusion_result,
                                     bool &is_cooldown_active,
                                     std::chrono::seconds &perception_drive_duration,
                                     int &perception_drive_cooldown_time_threshold,
                                     ThreadControl &thread_control);

    // Cross region handling
    void HandleExploreCrossRegionStates(CrossRegionRunningState &cross_region_state);

    // Recharge processing
    void ProcessingExplorationRecharge(const QRCodeLocationResult &qrcode_loc_result);

    // Exception processing
    void ProcessExplorationRechargeException(RechargeRunningState recharge_state,
                                             ThreadControl &thread_control);
    void ProcessExplorationCrossRegionException(CrossRegionRunningState cross_region_state);

    // Utility functions
    void ResetAndActivateCooldown();
    bool CanTriggerBottomObstacleAvoidance();
    void FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx);
    int PairNumber(int n);
    void PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result,
                                   const PerceptionFusionResult &fusion_result,
                                   ThreadControl &thread_control);

private:
    ExplorationConfig config_;
    ExplorationState state_;
    PerceptionStatusFilter perception_status_filter_;

    // Callbacks
    VelocityPublishCallback velocity_publish_callback_;
    FeatureUpdateCallback feature_update_callback_;
    EdgeFollowCallback edge_follow_disable_callback_;
    EdgeFollowCallback edge_follow_enable_callback_;
    CrossRegionCallback cross_region_disable_callback_;
    CrossRegionCallback cross_region_enable_callback_;
    MarkIdCallback mark_id_callback_;
    CrossRegionStateCallback cross_region_state_callback_;
    AreaCalcCallback area_calc_start_callback_;
    AreaCalcStopCallback area_calc_stop_callback_;
    RegionExploreResultCallback region_explore_result_callback_;
    LinearMotionCallback linear_motion_callback_;
};

} // namespace fescue_iox

#endif // EXPLORATION_HANDLER_HPP
