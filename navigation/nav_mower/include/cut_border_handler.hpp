#ifndef CUT_BORDER_HANDLER_HPP
#define CUT_BORDER_HANDLER_HPP

#include "data_type.hpp"
#include "obstacle_classification.hpp"
#include "perception_status_filter.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"

#include <chrono>
#include <cmath>
#include <functional>
#include <limits>
#include <memory>
#include <mutex>

namespace fescue_iox
{

// Forward declarations
struct ExceptionInfo;

class CutBorderHandler
{
public:
    struct CutBorderConfig
    {
        // Cut border parameters
        float mower_linear{0.15};
        float mower_angular{0.5};
        int edge_mode_direction{-1};
        float mark_distance_threshold{0.5};
        float camera_2_center_dis{0.37};
        float cross_region_adjust_yaw{1.57};
        float cross_region_adjust_displace{0.3};

        // Cooldown parameters
        int edge_perception_drive_cooldown_time_threshold{10};
        int qr_detection_cooldown_time_threshold{30};
        int mark_detection_cooldown_time_threshold{30};

        // Region exploration
        float recharge_distance_threshold{1.2};

        // Perception filter parameters
        int perception_filter_window_size{5};
        float perception_filter_confidence_threshold{0.6f};
        float perception_filter_debounce_time{1.0f};

        // Bottom obstacle avoidance
        float bottom_obstacle_avoidance_cooldown_time{5.0f};

        // Perception movement
        float perception_movement_debounce_time{2.0f};
    };

    struct CutBorderState
    {
        // Cut border mode state
        bool is_cut_border_mode_start{false};
        bool enter_multi_region_exploration{false};

        // Cooldown mechanism
        bool is_cooldown_active{false};
        bool is_first_enter_last_cooldown_time{true};
        std::chrono::steady_clock::time_point last_cooldown_time;
        std::chrono::seconds edge_perception_drive_duration{0};

        // Single region cut border
        int qr_code_detection_count{1};
        bool is_first_enter_cut_border_last_qr_detection_time{true};
        std::chrono::steady_clock::time_point last_qr_cut_border_detection_time;
        std::chrono::seconds qr_detection_duration{0};
        bool is_single_area_recharge{false};

        // Multi region cut border
        BeaconStatus beacon_status{-1, 0};
        int current_mark_id{-1};
        bool is_first_enter_last_mark_detection_time{true};
        std::chrono::steady_clock::time_point last_mark_detection_time;
        std::chrono::seconds mark_detection_duration{0};
        bool first_detection_beacon{true};
        int next_paired_beacon_id{-1};
        int region_count{1};

        // Cut border results
        bool is_first_cut_border_mode_end{true};

        // Perception movement state
        // PerceptionMovementState last_perception_movement_state{PerceptionMovementState::IDLE};
        std::chrono::steady_clock::time_point last_perception_movement_time;

        // Bottom obstacle avoidance
        std::chrono::steady_clock::time_point last_bottom_obstacle_avoidance_time;
    };

    // Callback types
    using VelocityPublishCallback = std::function<void(float, float, uint64_t)>;
    using FeatureUpdateCallback = std::function<void(ThreadControl)>;
    using EdgeFollowCallback = std::function<void()>;
    using CrossRegionCallback = std::function<void()>;
    using MarkIdCallback = std::function<bool(int)>;
    using CrossRegionStateCallback = std::function<void(CrossRegionRunningState)>;
    using CutBorderResultCallback = std::function<void(bool, bool)>;
    using LinearMotionCallback = std::function<void(float, float, float, int, float, bool)>;

public:
    CutBorderHandler(const CutBorderConfig &config);
    ~CutBorderHandler() = default;

    // Main cut border function
    void PerformCutBorder(MarkLocationResult &mark_loc_result,
                          const QRCodeLocationResult &qrcode_loc_result,
                          const PerceptionFusionResult &fusion_result,
                          CrossRegionRunningState &cross_region_state,
                          BehaviorRunningState &behavior_state,
                          bool is_behavior_loop,
                          const std::vector<BehaviorExceptionType> &triggered_exception_types,
                          ThreadControl &thread_control);

    // Cut border mode processing
    void ProcessCutBorderUnstakeMode(const QRCodeLocationResult &qrcode_loc_result,
                                     bool is_enable_unstake_mode,
                                     bool is_unstake_success,
                                     bool is_power_connected,
                                     bool mcu_triggers_cut_border);

    // Exception handling
    void HandleCutBorderMcuException(const ExceptionInfo &exception_info,
                                     ThreadControl &thread_control);

    // State management
    void ResetCutBorderState();
    const CutBorderState &GetState() const { return state_; }
    CutBorderState &GetState() { return state_; }

    // Callback setters
    void SetVelocityPublishCallback(VelocityPublishCallback callback) { velocity_publish_callback_ = callback; }
    void SetFeatureUpdateCallback(FeatureUpdateCallback callback) { feature_update_callback_ = callback; }
    void SetEdgeFollowDisableCallback(EdgeFollowCallback callback) { edge_follow_disable_callback_ = callback; }
    void SetEdgeFollowEnableCallback(EdgeFollowCallback callback) { edge_follow_enable_callback_ = callback; }
    void SetCrossRegionDisableCallback(CrossRegionCallback callback) { cross_region_disable_callback_ = callback; }
    void SetCrossRegionEnableCallback(CrossRegionCallback callback) { cross_region_enable_callback_ = callback; }
    void SetMarkIdCallback(MarkIdCallback callback) { mark_id_callback_ = callback; }
    void SetCrossRegionStateCallback(CrossRegionStateCallback callback) { cross_region_state_callback_ = callback; }
    void SetCutBorderResultCallback(CutBorderResultCallback callback) { cut_border_result_callback_ = callback; }
    void SetLinearMotionCallback(LinearMotionCallback callback) { linear_motion_callback_ = callback; }

private:
    // Beacon detection and processing
    void ProcessBeaconDetection(MarkLocationResult &mark_loc_result,
                                const PerceptionFusionResult &fusion_result,
                                bool &enter_multi_region_exploration,
                                bool &is_beacon_valid,
                                ThreadControl &thread_control);

    void ProcessSingleAreaCutBorderMode(const QRCodeLocationResult &qrcode_loc_result,
                                        const bool &enter_multi_region_exploration,
                                        ThreadControl &thread_control);

    void ProcessMultiAreaCutBorderMode(const MarkLocationResult &mark_loc_result,
                                       const PerceptionFusionResult &fusion_result,
                                       const bool &enter_multi_region_exploration,
                                       bool &is_beacon_valid,
                                       ThreadControl &thread_control);

    void HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result,
                                             const PerceptionFusionResult &fusion_result,
                                             bool &is_cooldown_active,
                                             ThreadControl &thread_control);

    void HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result,
                                     const PerceptionFusionResult &fusion_result,
                                     bool &is_cooldown_active,
                                     std::chrono::seconds &perception_drive_duration,
                                     int &perception_drive_cooldown_time_threshold,
                                     ThreadControl &thread_control);

    // Cross region handling
    void HandleCutBorderCrossRegionStates(CrossRegionRunningState &cross_region_state,
                                          ThreadControl &thread_control);

    // Recharge processing
    void ProcessingCutBorderRecharge(const QRCodeLocationResult &qrcode_loc_result);

    // Exception processing
    void ProcessCutBorderRechargeException(RechargeRunningState recharge_state,
                                           ThreadControl &thread_control);
    void ProcessCutBorderCrossRegionException(CrossRegionRunningState cross_region_state);

    // Utility functions
    void ResetAndActivateCooldown();
    bool CanTriggerBottomObstacleAvoidance();
    void FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx);
    int PairNumber(int n);
    void PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result,
                                   const PerceptionFusionResult &fusion_result,
                                   ThreadControl &thread_control);

private:
    CutBorderConfig config_;
    CutBorderState state_;
    PerceptionStatusFilter perception_status_filter_;

    // Callbacks
    VelocityPublishCallback velocity_publish_callback_;
    FeatureUpdateCallback feature_update_callback_;
    EdgeFollowCallback edge_follow_disable_callback_;
    EdgeFollowCallback edge_follow_enable_callback_;
    CrossRegionCallback cross_region_disable_callback_;
    CrossRegionCallback cross_region_enable_callback_;
    MarkIdCallback mark_id_callback_;
    CrossRegionStateCallback cross_region_state_callback_;
    CutBorderResultCallback cut_border_result_callback_;
    LinearMotionCallback linear_motion_callback_;
};

} // namespace fescue_iox

#endif // CUT_BORDER_HANDLER_HPP
