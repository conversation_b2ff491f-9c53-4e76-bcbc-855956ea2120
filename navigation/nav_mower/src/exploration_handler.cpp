#include "exploration_handler.hpp"

#include "mower.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

ExplorationHandler::ExplorationHandler(const ExplorationConfig &config)
    : config_(config)
    , perception_status_filter_(config.perception_filter_window_size,
                                config.perception_filter_confidence_threshold,
                                config.perception_filter_debounce_time)
{
    // Initialize state
    state_.last_cooldown_time = std::chrono::steady_clock::now();
    state_.last_qr_explore_detection_time = std::chrono::steady_clock::now();
    state_.last_mark_detection_time = std::chrono::steady_clock::now();
    state_.last_recharge_cross_region_time = std::chrono::steady_clock::now();
    state_.last_perception_movement_time = std::chrono::steady_clock::now();
    state_.last_bottom_obstacle_avoidance_time = std::chrono::steady_clock::now();

    // Initialize exploration results
    state_.master_region_explore_result.is_exist = false;
    state_.master_region_explore_result.area = 0.0;
    state_.master_region_explore_result.perimeter = 0.0;
    state_.master_region_explore_result.charge_station_flag = false;
    state_.master_region_explore_result.beacon_id = -1;

    state_.slave_region_explore_result.is_exist = false;
    state_.slave_region_explore_result.area = 0.0;
    state_.slave_region_explore_result.perimeter = 0.0;
    state_.slave_region_explore_result.charge_station_flag = false;
    state_.slave_region_explore_result.beacon_id = -1;
}

void ExplorationHandler::PerformExploration(MarkLocationResult &mark_loc_result,
                                            const QRCodeLocationResult &qrcode_loc_result,
                                            const PerceptionFusionResult &fusion_result,
                                            CrossRegionRunningState &cross_region_state,
                                            BehaviorRunningState & /*behavior_state*/,
                                            bool /*is_behavior_loop*/,
                                            const std::vector<BehaviorExceptionType> & /*triggered_exception_types*/,
                                            ThreadControl &thread_control)
{
    switch (thread_control)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    {
        LOG_INFO_THROTTLE(1000, "[ExplorationHandler] In edge-following or undefined mode");

        bool is_beacon_valid = false; // Default: beacon invalid
        ProcessBeaconDetection(mark_loc_result, fusion_result, state_.enter_multi_region_exploration, is_beacon_valid, thread_control);

        if (!state_.enter_multi_region_exploration)
        {
            ProcessSingleAreaExplorationMode(qrcode_loc_result, state_.enter_multi_region_exploration, thread_control);
        }
        else
        {
            ProcessMultiAreaExplorationMode(mark_loc_result, fusion_result, state_.enter_multi_region_exploration, is_beacon_valid, thread_control);
        }

        break;
    }

    case ThreadControl::CROSS_REGION_THREAD:
    {
        LOG_INFO_THROTTLE(1000, "[ExplorationHandler] In cross-region mode");
        HandleExploreCrossRegionStates(cross_region_state);
        break;
    }

    case ThreadControl::RECHARGE_THREAD:
    {
        LOG_INFO_THROTTLE(1000, "[ExplorationHandler] In recharge mode");
        ProcessingExplorationRecharge(qrcode_loc_result);

        if (state_.is_first_region_explore_mode_end)
        {
            if (velocity_publish_callback_)
            {
                velocity_publish_callback_(0.0, 0.0, 1000);
            }
            state_.is_first_region_explore_mode_end = false;
        }
        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        LOG_INFO_THROTTLE(1000, "[ExplorationHandler] In Behavior mode");
        // Handle behavior state - this would need to be implemented based on requirements
        // CheckVelocityAndUpdateState(behavior_state, is_behavior_loop, triggered_exception_types);
        break;
    }

    default:
        LOG_INFO_THROTTLE(1000, "[ExplorationHandler] In other function mode");
        break;
    }
}

void ExplorationHandler::ProcessExplorationUnstakeMode(const QRCodeLocationResult & /*qrcode_loc_result*/,
                                                       bool is_enable_unstake_mode,
                                                       bool is_unstake_success,
                                                       bool is_power_connected,
                                                       bool mcu_triggers_region_exploration)
{
    if (is_enable_unstake_mode && !is_unstake_success && /* Unstake mode enabled && not successful */
        is_power_connected &&                            /* Robot is powered */
        mcu_triggers_region_exploration)                 /* MCU exploration request */
    {
        LOG_INFO("[ExplorationHandler] Exploration mode. Start unstake mode");
        // Note: The actual unstake operation would be handled by the main mower class
        // This handler only manages the exploration-specific logic
    }
}

void ExplorationHandler::HandleExplorationMcuException(const ExceptionInfo &exception_info,
                                                       ThreadControl &thread_control)
{
    const auto &recharge_state = exception_info.recharge_state;
    const auto &cross_region_state = exception_info.cross_region_state;
    // const auto &is_slipping = exception_info.is_slipping;
    // const auto &mcu_exception_status = exception_info.mcu_exception_status;
    // const auto &is_stuck = exception_info.is_stuck;
    (void)exception_info; // Suppress unused parameter warning

    // If charging station QR code detected
    if (thread_control == ThreadControl::RECHARGE_THREAD &&
        (recharge_state == RechargeRunningState::ADJUST_TO_STATION ||
         recharge_state == RechargeRunningState::ACCURATE_DOCK)) // Charging station QR code found /**Cannot perform recovery mode */
    {
        ProcessExplorationRechargeException(recharge_state, thread_control);
    }
    // If beacon QR code detected (cross-region case)
    else if (thread_control == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON &&
             cross_region_state != CrossRegionRunningState::UNDEFINED &&
             cross_region_state != CrossRegionRunningState::FINISH) // Beacon found /**Cannot perform recovery mode */
    {
        ProcessExplorationCrossRegionException(cross_region_state);
    }
    // Other exceptions, enter recovery mode
    else
    {
        // Recovery mode would be handled by the main mower class
        LOG_WARN("[ExplorationHandler] Exception detected, recovery mode needed");
    }
}

void ExplorationHandler::ResetExplorationState()
{
    state_.is_region_explore_mode_start = false;
    state_.enter_multi_region_exploration = false;
    state_.is_cooldown_active = false;
    state_.is_first_enter_last_cooldown_time = true;
    state_.qr_code_detection_count = 1;
    state_.is_first_enter_explore_last_qr_detection_time = true;
    state_.is_single_area_recharge = false;
    state_.beacon_status = BeaconStatus(-1, 0);
    state_.current_mark_id = -1;
    state_.is_first_enter_last_mark_detection_time = true;
    state_.first_detection_beacon = true;
    state_.next_paired_beacon_id = -1;
    state_.region_count = 1;
    state_.first_recharge_cross_region = true;
    state_.cross_recharge_detection_beacon = true;
    state_.cross_recharge_mark_id = -1;
    state_.cross_recharge_beacon_status = BeaconStatus(-1, 0);
    state_.recharge_edge_follow = false;
    state_.is_master_region = true;
    state_.is_first_region_explore_mode_end = true;
    // state_.last_perception_movement_state = PerceptionMovementState::IDLE;

    // Reset time points
    auto now = std::chrono::steady_clock::now();
    state_.last_cooldown_time = now;
    state_.last_qr_explore_detection_time = now;
    state_.last_mark_detection_time = now;
    state_.last_recharge_cross_region_time = now;
    state_.last_perception_movement_time = now;
    state_.last_bottom_obstacle_avoidance_time = now;

    // Reset durations
    state_.edge_perception_drive_duration = std::chrono::seconds(0);
    state_.qr_detection_duration = std::chrono::seconds(0);
    state_.mark_detection_duration = std::chrono::seconds(0);
    state_.recharge_cross_region_duration = std::chrono::seconds(0);
}

void ExplorationHandler::ResetAndActivateCooldown()
{
    // Reset cooldown timestamp and activate cooldown mechanism
    state_.last_cooldown_time = std::chrono::steady_clock::now();
    state_.is_cooldown_active = true;
}

bool ExplorationHandler::CanTriggerBottomObstacleAvoidance()
{
    auto current_time = std::chrono::steady_clock::now();
    auto time_since_last_avoidance = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - state_.last_bottom_obstacle_avoidance_time);

    return time_since_last_avoidance.count() >= config_.bottom_obstacle_avoidance_cooldown_time;
}

int ExplorationHandler::PairNumber(int n)
{
    // Implementation of pair number logic
    if (n % 2 == 0)
    {
        return n - 1;
    }
    else
    {
        return n + 1;
    }
}

void ExplorationHandler::FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx)
{
    shortest_dis_inx = -1;
    float min_distance = std::numeric_limits<float>::max();

    for (size_t i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        if (mark_id_distance_vec[i].distance < config_.mark_distance_threshold &&
            mark_id_distance_vec[i].distance < min_distance)
        {
            min_distance = mark_id_distance_vec[i].distance;
            shortest_dis_inx = static_cast<int>(i);
        }
    }
}

void ExplorationHandler::ProcessBeaconDetection(MarkLocationResult &mark_loc_result,
                                                const PerceptionFusionResult &fusion_result,
                                                bool &enter_multi_region_exploration,
                                                bool &is_beacon_valid,
                                                ThreadControl &thread_control)
{
    LOG_INFO_THROTTLE(500, "[ExplorationHandler] Start region exploration mode");

    // First check for bottom obstacles before processing beacon detection
    const auto &occupancy_grid = fusion_result.occupancy_grid;
    if (!occupancy_grid.grid.empty() && occupancy_grid.width > 0 && occupancy_grid.height > 0)
    {
        bool has_bottom_obstacle_original = ObstacleClassification::HasBottomObstacle(occupancy_grid.grid, occupancy_grid.height, occupancy_grid.width, 0.01f);
        bool has_bottom_beacon_obstacle = ObstacleClassification::HasBottomBeaconObstacle(occupancy_grid.grid, occupancy_grid.height, occupancy_grid.width);
        bool has_bottom_obstacle = has_bottom_obstacle_original && has_bottom_beacon_obstacle;

        if (has_bottom_obstacle && CanTriggerBottomObstacleAvoidance())
        {
            LOG_INFO("[ExplorationHandler] Bottom obstacle detected (original: {}, beacon: {}), moving backward for fixed distance",
                     has_bottom_obstacle_original, has_bottom_beacon_obstacle);

            if (edge_follow_disable_callback_)
            {
                edge_follow_disable_callback_();
            }

            if (linear_motion_callback_)
            {
                linear_motion_callback_(0.5, 0.0, config_.mower_linear, -1, 0.0f, true);
            }

            // Update the last avoidance time
            state_.last_bottom_obstacle_avoidance_time = std::chrono::steady_clock::now();
            return;
        }
        else if (has_bottom_obstacle)
        {
            LOG_INFO_THROTTLE(1000, "[ExplorationHandler] Bottom obstacle detected but still in cooldown period, skipping avoidance");
        }
    }

    if (mark_loc_result.mark_perception_status == 0) // Filtered perception did not detect beacon
    {
        LOG_INFO_THROTTLE(1000, "[ExplorationHandler] Filtered perception did not detect beacon (raw: {}, filtered: {}), enable edge following",
                          mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_status);

        thread_control = ThreadControl::PERCEPTION_EDGE_THREAD;
        if (feature_update_callback_)
        {
            feature_update_callback_(thread_control);
        }
    }
    else // Filtered perception detected beacon
    {
        LOG_INFO("[ExplorationHandler] Filtered perception detected beacon (raw: {}, filtered: {})",
                 mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_status);

        if (mark_loc_result.mark_id_distance.size() <= 0) // No value in mark_id_distance
        {
            LOG_INFO("[ExplorationHandler] No value in mark_id_distance from localization");
            HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, state_.is_cooldown_active, thread_control);
        }
        else // mark_id_distance has value
        {
            LOG_INFO("[ExplorationHandler] mark_id_distance from localization has value");

            // Determine if beacon is valid (distance < threshold is considered valid)
            int shortest_dis_inx = -1;
            std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
            FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

            if (shortest_dis_inx == -1) // If beacon invalid, do nothing, continue previous action
            {
                LOG_INFO("[ExplorationHandler] Cross-region beacon invalid");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, state_.is_cooldown_active, thread_control);
            }
            else // If beacon valid, check stack container
            {
                LOG_INFO("[ExplorationHandler] Cross-region beacon valid");
                LOG_INFO("[ExplorationHandler] Valid beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                //! Beacon valid, can enter multi-region exploration
                state_.current_mark_id = mark_id_distance_vec[shortest_dis_inx].mark_id;
                if (state_.first_detection_beacon)
                {
                    state_.beacon_status = BeaconStatus(state_.current_mark_id, 1);
                    state_.first_detection_beacon = false;
                }

                enter_multi_region_exploration = true;
                is_beacon_valid = true;
            }
        }
    }
}

void ExplorationHandler::ProcessSingleAreaExplorationMode(const QRCodeLocationResult &qrcode_loc_result,
                                                          const bool &enter_multi_region_exploration,
                                                          ThreadControl &thread_control)
{
    if (!enter_multi_region_exploration) /* Not entering multi-region exploration */
    {
        LOG_INFO_THROTTLE(1000, "[ExplorationHandler] Start single area exploration mode");

        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            LOG_INFO("[ExplorationHandler] QR code pose detected");

            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < config_.recharge_distance_threshold)
            {
                LOG_INFO("[ExplorationHandler] QR code pose within recharge distance threshold");

                auto current_time = std::chrono::steady_clock::now();
                state_.qr_detection_duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - state_.last_qr_explore_detection_time);

                LOG_INFO("[ExplorationHandler] Charging station QR code detection cooldown timer (seconds): ({})", state_.qr_detection_duration.count());
                if (state_.qr_detection_duration.count() > config_.qr_detection_cooldown_time_threshold) // Increase timer
                {
                    state_.qr_code_detection_count++;
                    state_.last_qr_explore_detection_time = std::chrono::steady_clock::now();
                    LOG_INFO("[ExplorationHandler] Detected valid charging station QR code pose, current detection count: {}", state_.qr_code_detection_count);
                }
            }
            else
            {
                LOG_INFO("[ExplorationHandler] QR code pose not within recharge distance threshold");
            }
        }
        else
        {
            LOG_INFO("[ExplorationHandler] QR code pose not detected");
        }

        // Use QR code detection to determine when to stop edge following and switch to recharge
        if (state_.qr_code_detection_count >= 2)
        {
            LOG_INFO("[ExplorationHandler] Detected valid recharge QR code pose twice, switching to recharge mode");
            thread_control = ThreadControl::RECHARGE_THREAD;
            state_.is_single_area_recharge = true;

            // Reset state
            state_.qr_code_detection_count = 1;
        }
    }
}

void ExplorationHandler::ProcessMultiAreaExplorationMode(const MarkLocationResult &mark_loc_result,
                                                         const PerceptionFusionResult &fusion_result,
                                                         const bool &enter_multi_region_exploration,
                                                         bool &is_beacon_valid,
                                                         ThreadControl &thread_control)
{
    if (enter_multi_region_exploration) /* Entering multi-region exploration */
    {
        LOG_INFO("[ExplorationHandler] Start multi-region exploration mode");

        if (is_beacon_valid) // Beacon is valid
        {
            // Reset cooldown timestamp and activate cooldown mechanism
            state_.last_cooldown_time = std::chrono::steady_clock::now();
            state_.is_cooldown_active = true;

            LOG_INFO("[ExplorationHandler] Detected valid beacon QR code pose, current detection count: {}", state_.beacon_status.beacon_look_count);
            LOG_INFO("[ExplorationHandler] Current detected mark_id: {}", state_.current_mark_id);

            // Initialize only on first entry
            if (state_.is_first_enter_last_mark_detection_time)
            {
                state_.last_mark_detection_time = std::chrono::steady_clock::now(); // Beacon detection start timing
                state_.is_first_enter_last_mark_detection_time = false;
                LOG_INFO("[ExplorationHandler] Beacon detection start timing");
            }

            auto current_time = std::chrono::steady_clock::now();
            state_.mark_detection_duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - state_.last_mark_detection_time);

            LOG_INFO("[ExplorationHandler] Beacon detection cooldown timer (seconds): ({})", state_.mark_detection_duration.count());
            if (state_.mark_detection_duration.count() > config_.mark_detection_cooldown_time_threshold)
            {
                if (state_.current_mark_id == state_.beacon_status.mark_id) // Same mark_id
                {
                    state_.beacon_status.beacon_look_count++;
                    state_.last_mark_detection_time = std::chrono::steady_clock::now();
                    LOG_WARN("[ExplorationHandler] Detected valid beacon QR code pose, current detection count: {}", state_.beacon_status.beacon_look_count);
                    LOG_WARN("[ExplorationHandler] Same mark_id, current detected mark_id: {}", state_.current_mark_id);
                }
                else // Different mark_id
                {
                    state_.beacon_status = BeaconStatus(state_.current_mark_id, 1);
                    state_.last_mark_detection_time = std::chrono::steady_clock::now();
                    LOG_WARN("[ExplorationHandler] Detected valid beacon QR code pose, current detection count: {}", state_.beacon_status.beacon_look_count);
                    LOG_WARN("[ExplorationHandler] Different mark_id, current detected mark_id: {}", state_.current_mark_id);
                    LOG_WARN("[ExplorationHandler] Different mark_id, last detected mark_id: {}", state_.beacon_status.mark_id);
                }
            }

            if (state_.beacon_status.beacon_look_count >= 2)
            {
                // Start cross-region process
                LOG_INFO("[ExplorationHandler] Beacon detected more than twice, starting cross-region process");

                thread_control = ThreadControl::CROSS_REGION_THREAD;
                if (feature_update_callback_)
                {
                    feature_update_callback_(thread_control);
                }
                if (edge_follow_disable_callback_)
                {
                    edge_follow_disable_callback_();
                }
                state_.is_single_area_recharge = false;

                // Reset state
                state_.next_paired_beacon_id = PairNumber(state_.current_mark_id);
                state_.beacon_status = BeaconStatus(state_.next_paired_beacon_id, 1);
                LOG_INFO("[ExplorationHandler] Next paired beacon id is {}", state_.next_paired_beacon_id);
            }
            else
            {
                // Continue edge following
                LOG_INFO("[ExplorationHandler] Beacon detection not more than twice, continue");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, state_.is_cooldown_active, thread_control);
                if (cross_region_disable_callback_)
                {
                    cross_region_disable_callback_();
                }
            }
        }
    }
}

void ExplorationHandler::HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result,
                                                             const PerceptionFusionResult &fusion_result,
                                                             bool &is_cooldown_active,
                                                             ThreadControl &thread_control)
{
    if (is_cooldown_active) // Cooldown mechanism active
    {
        LOG_DEBUG("[ExplorationHandler] Cooldown mechanism activated");

        auto current_time = std::chrono::steady_clock::now();
        state_.edge_perception_drive_duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - state_.last_cooldown_time);

        // Print time difference
        LOG_DEBUG("[ExplorationHandler] Edge perception drive cooldown timer (seconds): ({})", state_.edge_perception_drive_duration.count());
        HandleEdgeCooldownMechanism(mark_loc_result, fusion_result, is_cooldown_active,
                                    state_.edge_perception_drive_duration, config_.edge_perception_drive_cooldown_time_threshold, thread_control);
    }
    else // Cooldown mechanism not active
    {
        LOG_DEBUG("[ExplorationHandler] Cooldown mechanism not activated");
        LOG_DEBUG("[ExplorationHandler] Enable perception drive, disable edge following");

        thread_control = ThreadControl::UNDEFINED;
        if (feature_update_callback_)
        {
            feature_update_callback_(thread_control);
        }

        PerceptionBasedAdjustment(mark_loc_result, fusion_result, thread_control);
    }
}

void ExplorationHandler::HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result,
                                                     const PerceptionFusionResult &fusion_result,
                                                     bool &is_cooldown_active,
                                                     std::chrono::seconds &perception_drive_duration,
                                                     int &perception_drive_cooldown_time_threshold,
                                                     ThreadControl &thread_control)
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[ExplorationHandler] Timer exceeded ({}s), cooldown ended", perception_drive_cooldown_time_threshold);
        LOG_INFO("[ExplorationHandler] Enable perception drive, disable edge following");

        thread_control = ThreadControl::UNDEFINED;
        if (feature_update_callback_)
        {
            feature_update_callback_(thread_control);
        }

        PerceptionBasedAdjustment(mark_loc_result, fusion_result, thread_control);
    }
    else
    {
        // Cooldown not finished, skip execution
        LOG_INFO("[ExplorationHandler] Cooldown not finished, not exceeding ({}s)", perception_drive_cooldown_time_threshold);

        thread_control = ThreadControl::PERCEPTION_EDGE_THREAD;
        if (feature_update_callback_)
        {
            feature_update_callback_(thread_control);
        }
        LOG_INFO("[ExplorationHandler] Enable edge following");
    }
}

void ExplorationHandler::HandleExploreCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    // This would handle cross-region state transitions for exploration mode
    // Implementation would depend on specific cross-region logic
    LOG_INFO_THROTTLE(1000, "[ExplorationHandler] Handling cross-region state: {}", static_cast<int>(cross_region_state));
}

void ExplorationHandler::ProcessingExplorationRecharge(const QRCodeLocationResult &qrcode_loc_result)
{
    LOG_INFO_THROTTLE(1000, "[ExplorationHandler] Processing exploration recharge");

    // Handle recharge logic for exploration mode
    if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
    {
        LOG_INFO("[ExplorationHandler] QR code detected during recharge");
        // Additional recharge processing logic would go here
    }
}

void ExplorationHandler::ProcessExplorationRechargeException(RechargeRunningState /*recharge_state*/,
                                                             ThreadControl &thread_control)
{
    LOG_WARN_THROTTLE(500, "[ExplorationHandler] Charging station QR code detected");

    LOG_INFO_THROTTLE(500, "[ExplorationHandler] Switch to recharge mode");

    // Switch to recharge thread and update feature selection
    thread_control = ThreadControl::RECHARGE_THREAD;
    if (feature_update_callback_)
    {
        feature_update_callback_(thread_control);
    }
}

void ExplorationHandler::ProcessExplorationCrossRegionException(CrossRegionRunningState /*cross_region_state*/)
{
    LOG_WARN_THROTTLE(500, "[ExplorationHandler] Cross-region beacon detected");

    // Handle cross-region exception logic
    LOG_INFO_THROTTLE(500, "[ExplorationHandler] Switch to cross-region mode");
}

void ExplorationHandler::PerceptionBasedAdjustment(const MarkLocationResult & /*mark_loc_result*/,
                                                   const PerceptionFusionResult & /*fusion_result*/,
                                                   ThreadControl & /*thread_control*/)
{
    // This is a placeholder for perception-based adjustment logic
    // The actual implementation would depend on the specific perception algorithms
    LOG_DEBUG("[ExplorationHandler] Performing perception-based adjustment");

    // Example: Check if we need to move forward or backward based on perception
    // This would be implemented based on the specific requirements
}

} // namespace fescue_iox
